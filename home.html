<!DOCTYPE html>
<html class="dark" lang="en">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>IsotopeAI</title>
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-500..200&display=swap" rel="stylesheet" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#F4C753",
                        "background-light": "#f8f7f6",
                        "background-dark": "#221d10",
                        "card-dark": "#1C1C1E"
                    },
                    fontFamily: {
                        display: "Inter"
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                        lg: "1rem",
                        xl: "1.5rem",
                        full: "9999px"
                    }
                }
            }
        };
    </script>
    <style>
        .material-symbols-outlined {
            font-variation-settings: "FILL" 0, "wght" 400, "GRAD" 0, "opsz" 24;
        }

        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display text-gray-800 dark:text-gray-200">
    <div class="flex h-screen flex-col">
        <header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="w-10"></div>
                <div class="flex flex-col items-center">
                    <h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">IsotopeAI</h1>
                    <p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Dashboard</p>
                </div>
                <button class="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 transition-colors">
                    <svg fill="none" height="20" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V19a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 16.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H5a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 6.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33A1.65 1.65 0 0 0 12.91 3V5a2 2 0 0 1 2 2h2a2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1.51 1 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V15Z"></path>
                    </svg>
                </button>
            </div>
        </header>
        <main class="flex-1 overflow-y-auto px-6 py-8 space-y-8">
            <div class="space-y-2">
                <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Good morning, Friend!</h1>
                <p class="text-gray-600 dark:text-gray-400">Ready to achieve your goals today?</p>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Quick Start</h2>
                    <button class="px-6 py-3 rounded-full bg-gradient-to-r from-primary to-yellow-400 text-gray-900 font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                        Focus
                    </button>
                </div>
                <div class="grid grid-cols-2 gap-6">
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">2h 15m</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Today's Focus</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">🔥 12</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Current Streak</p>
                    </div>
                </div>
            </div>
            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">My Progress</h2>
                    <div class="flex items-center space-x-2 bg-gray-100/80 dark:bg-gray-700/50 p-1 rounded-full text-sm font-medium">
                        <button class="px-4 py-2 rounded-full bg-white dark:bg-gray-600 text-gray-800 dark:text-gray-100 shadow-sm">Weekly</button>
                        <button class="px-4 py-2 rounded-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">Monthly</button>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-8">
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">15.5h</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Weekly Study</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">28 days</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Longest Streak</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">85%</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Goal Comp.</p>
                    </div>
                </div>

                <div class="h-48 w-full bg-gray-50/50 dark:bg-gray-700/20 rounded-xl p-4">
                    <div class="flex h-full items-end justify-between space-x-2">
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[60%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Mon</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[80%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Tue</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary rounded-t-lg h-[95%] hover:bg-primary/80 transition-colors shadow-lg"></div>
                            <span class="text-xs text-primary font-bold">Wed</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[50%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Thu</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[70%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Fri</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[40%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Sat</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-gray-300 dark:bg-gray-600 rounded-t-lg h-[20%]"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Sun</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid gap-6 sm:grid-cols-2">
                <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                    <div class="flex items-center justify-between mb-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Focus Time</p>
                        <div class="flex items-center gap-1 text-sm">
                            <p class="font-medium text-green-500">+15%</p>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mb-2">12h 30m</p>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">This Week</p>
                    <div class="h-32 bg-gray-50/50 dark:bg-gray-700/20 rounded-xl p-3">
                        <svg fill="none" height="100%" preserveaspectratio="none" viewbox="0 0 300 140" width="100%" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 88C11.5455 88 11.5455 17 23.0909 17C34.6364 17 34.6364 33 46.1818 33C57.7273 33 57.7273 75 69.2727 75C80.8182 75 80.8182 26 92.3636 26C103.909 26 103.909 81 115.455 81C127 81 127 49 138.545 49C150.091 49 150.091 36 161.636 36C173.182 36 173.182 97 184.727 97C196.273 97 196.273 119 207.818 119C219.364 119 219.364 1 230.909 1C242.455 1 242.455 65 254 65C265.545 65 265.545 103 277.091 103C288.636 103 288.636 20 300 20" stroke="url(#line-gradient)" stroke-linecap="round" stroke-width="3"></path>
                            <path d="M0 88C11.5455 88 11.5455 17 23.0909 17C34.6364 17 34.6364 33 46.1818 33C57.7273 33 57.7273 75 69.2727 75C80.8182 75 80.8182 26 92.3636 26C103.909 26 103.909 81 115.455 81C127 81 127 49 138.545 49C150.091 49 150.091 36 161.636 36C173.182 36 173.182 97 184.727 97C196.273 97 196.273 119 207.818 119C219.364 119 219.364 1 230.909 1C242.455 1 242.455 65 254 65C265.545 65 265.545 103 277.091 103C288.636 103 288.636 20 300 20V140H0V88Z" fill="url(#area-gradient)"></path>
                            <defs>
                                <lineargradient gradientunits="userSpaceOnUse" id="line-gradient" x1="0" x2="300" y1="0" y2="0">
                                    <stop stop-color="#F4C753" stop-opacity="0"></stop>
                                    <stop offset="0.5" stop-color="#F4C753"></stop>
                                    <stop offset="1" stop-color="#F4C753" stop-opacity="0"></stop>
                                </lineargradient>
                                <lineargradient gradientunits="userSpaceOnUse" id="area-gradient" x1="150" x2="150" y1="0" y2="140">
                                    <stop stop-color="#F4C753" stop-opacity="0.3"></stop>
                                    <stop offset="1" stop-color="#F4C753" stop-opacity="0"></stop>
                                </lineargradient>
                            </defs>
                        </svg>
                    </div>
                </div>
                <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                    <div class="flex items-center justify-between mb-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tasks Completed</p>
                        <div class="flex items-center gap-1 text-sm">
                            <p class="font-medium text-green-500">+20%</p>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mb-2">25</p>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">This Week</p>
                    <div class="h-32 bg-gray-50/50 dark:bg-gray-700/20 rounded-xl p-3">
                        <div class="grid h-full grid-cols-7 items-end gap-2">
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 40%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 60%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 30%"></div>
                            <div class="rounded-t-lg bg-primary hover:bg-primary/80 transition-colors shadow-lg" style="height: 80%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 40%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 30%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 10%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <h2 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Next Up</h2>
                <div class="flex items-center space-x-4 p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                    <div class="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-xl">
                        <span class="material-symbols-outlined text-blue-500">science</span>
                    </div>
                    <div class="flex-1">
                        <p class="font-semibold text-gray-900 dark:text-white">Chemistry Lab Report</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Due in 2 days</p>
                    </div>
                    <button class="p-3 rounded-full bg-primary/20 text-primary hover:bg-primary/30 transition-colors">
                        <span class="material-symbols-outlined">arrow_forward</span>
                    </button>
                </div>
            </div>
            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <h2 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Achieve Your Goals</h2>
                <div class="space-y-6">
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-800 dark:text-gray-200">Master Calculus</span>
                            <span class="font-medium text-gray-500 dark:text-gray-400">75%</span>
                        </div>
                        <div class="w-full bg-gray-200/80 dark:bg-gray-700/50 rounded-full h-3">
                            <div class="bg-gradient-to-r from-cyan-400 to-blue-500 h-3 rounded-full shadow-sm" style="width: 75%"></div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-800 dark:text-gray-200">Ace Physics Exam</span>
                            <span class="font-medium text-gray-500 dark:text-gray-400">50%</span>
                        </div>
                        <div class="w-full bg-gray-200/80 dark:bg-gray-700/50 rounded-full h-3">
                            <div class="bg-gradient-to-r from-green-400 to-teal-500 h-3 rounded-full shadow-sm" style="width: 50%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <h2 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Today's Tasks & Habits</h2>
                <div class="space-y-4 mb-6">
                    <div class="flex items-center space-x-4 p-3 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <button class="w-6 h-6 border-2 border-red-400 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"></button>
                        <p class="font-medium flex-1 text-gray-900 dark:text-white">Review Quantum Mechanics notes</p>
                        <span class="text-xs font-medium text-red-400 bg-red-400/20 px-3 py-1 rounded-full">Urgent</span>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <button class="w-6 h-6 border-2 border-gray-400 rounded-full hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"></button>
                        <p class="font-medium flex-1 text-gray-900 dark:text-white">Practice 10 integrals</p>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl opacity-75">
                        <button class="w-6 h-6 border-2 border-primary rounded-full flex items-center justify-center bg-primary/10">
                            <span class="material-symbols-outlined text-primary text-base">check</span>
                        </button>
                        <p class="font-medium flex-1 line-through text-gray-500 dark:text-gray-400">Daily meditation</p>
                    </div>
                </div>
                <div class="relative">
                    <input class="w-full bg-gray-50/80 dark:bg-gray-700/30 rounded-xl py-4 pl-4 pr-14 focus:ring-2 focus:ring-primary/50 border-transparent focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400" placeholder="Add a quick task..." type="text" />
                    <button class="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-gradient-to-r from-primary to-yellow-400 text-gray-900 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                        <span class="material-symbols-outlined">add</span>
                    </button>
                </div>
            </div>
        </main>
        <footer class="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
            <div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="#">
                    <div class="p-1">
                        <svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
                            <path d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-semibold">Home</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="#">
                    <div class="p-1">
                        <svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
                            <path d="M221.87,83.16A104.1,104.1,0,1,1,195.67,49l22.67-22.68a8,8,0,0,1,11.32,11.32L167.6,99.71h0l-37.71,37.71-23.95,23.95a40,40,0,0,0,62-35.67,8,8,0,1,1,16-.9,56,56,0,0,1-95.5,42.79h0a56,56,0,0,1,73.13-84.43L184.3,60.39a87.88,87.88,0,1,0,23.13,29.67,8,8,0,0,1,14.44-6.9Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Focus</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="#">
                    <div class="p-1">
                        <svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
                            <path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Track</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="#">
                    <div class="p-1">
                        <svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
                            <path d="M232,64H208V56a16,16,0,0,0-16-16H64A16,16,0,0,0,48,56v8H24A16,16,0,0,0,8,80V96a40,40,0,0,0,40,40h3.65A80.13,80.13,0,0,0,120,191.61V216H96a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16H136V191.58c31.94-3.23,58.44-25.64,68.08-55.58H208a40,40,0,0,0,40-40V80A16,16,0,0,0,232,64ZM48,120A24,24,0,0,1,24,96V80H48v32q0,4,.39,8Zm144-8.9c0,35.52-28.49,64.64-63.51,64.9H128a64,64,0,0,1-64-64V56H192ZM232,96a24,24,0,0,1-24,24h-.5a81.81,81.81,0,0,0,.5-8.9V80h24Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Achieve</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="#">
                    <div class="p-1">
                        <svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
                            <path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Tasks</p>
                </a>
            </div>
        </footer>
    </div>
</body>
</html>