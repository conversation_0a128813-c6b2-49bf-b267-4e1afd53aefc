<!DOCTYPE html>

<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>IsotopeAI Tasks</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>tailwind.config = {darkMode: "class", theme: {extend: {colors: {primary: "#F4C753", "background-light": "#f8f7f6", "background-dark": "#221d10"}, fontFamily: {display: "Inter"}, borderRadius: {DEFAULT: "0.5rem", lg: "1rem", xl: "1.5rem", full: "9999px"}}}};</script>
<style>
      .material-symbols-outlined {
        font-variation-settings:
        'FILL' 0,
        'wght' 400,
        'GRAD' 0,
        'opsz' 24
      }
      .material-symbols-outlined.filled {
        font-variation-settings:
        'FILL' 1,
        'wght' 400,
        'GRAD' 0,
        'opsz' 24
      }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display">
<div class="flex flex-col h-screen justify-between">
<main class="flex-grow overflow-y-auto">
<header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
<div class="flex items-center justify-between px-6 py-4">
<div class="w-10"></div>
<div class="flex flex-col items-center">
<h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Tasks</h1>
<p class="text-xs text-gray-500 dark:text-gray-400 font-medium">& Habits</p>
</div>
<button class="flex h-10 w-10 items-center justify-center rounded-xl bg-primary/10 text-primary hover:bg-primary/20 transition-colors">
<span class="material-symbols-outlined text-lg">add_circle</span>
</button>
</div>
<div class="px-4 pb-2">
<div class="flex items-center gap-2">
<button class="flex items-center gap-1.5 px-3 py-1.5 bg-slate-200 dark:bg-slate-800 rounded-full text-slate-700 dark:text-slate-200 text-sm font-medium">
<span class="material-symbols-outlined text-base">swap_vert</span>
                            Sort
                        </button>
<button class="flex items-center gap-1.5 px-3 py-1.5 bg-slate-200 dark:bg-slate-800 rounded-full text-slate-700 dark:text-slate-200 text-sm font-medium">
<span class="material-symbols-outlined text-base">filter_list</span>
                            Filter
                        </button>
</div>
</div>
</header>
<div class="px-4">
<div class="bg-white dark:bg-slate-800/50 p-4 rounded-xl mt-4">
<h3 class="text-base font-bold text-slate-900 dark:text-white mb-2">Smart Suggestions</h3>
<div class="flex gap-2">
<button class="flex-1 text-left bg-slate-100 dark:bg-slate-700/50 p-3 rounded-lg text-sm">
<p class="font-semibold text-slate-800 dark:text-white">Plan 'Project Y'</p>
<p class="text-xs text-slate-500 dark:text-slate-400">Based on your goals</p>
</button>
<button class="flex-1 text-left bg-slate-100 dark:bg-slate-700/50 p-3 rounded-lg text-sm">
<p class="font-semibold text-slate-800 dark:text-white">Evening Walk</p>
<p class="text-xs text-slate-500 dark:text-slate-400">Trending habit</p>
</button>
</div>
</div>
<div class="flex items-center justify-between mt-6 mb-3">
<h2 class="text-xl font-bold text-slate-900 dark:text-white">Today's List</h2>
<a class="text-sm font-medium text-primary dark:text-primary" href="#">View All</a>
</div>
<ul class="space-y-2">
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<div class="absolute inset-0 rounded-full border-2 border-primary/20 dark:border-primary/40"></div>
<div class="absolute inset-1 rounded-full border-2 border-primary/20 dark:border-primary/40 transform rotate-45"></div>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white">Morning Meditation</p>
<p class="text-sm text-slate-500 dark:text-slate-400">8:00 AM</p>
</div>
</div>
<div class="flex items-center gap-2">
<span class="text-xs font-semibold text-primary/80 dark:text-primary/70">🔥 5</span>
<button class="w-6 h-6 rounded-full border-2 border-slate-300 dark:border-slate-600 flex items-center justify-center"></button>
</div>
</li>
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg opacity-50">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<div class="absolute inset-0 rounded-full bg-primary/20 dark:bg-primary/30"></div>
<span class="material-symbols-outlined text-primary">check</span>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white line-through">Review Daily Goals</p>
<p class="text-sm text-slate-500 dark:text-slate-400">9:00 AM</p>
</div>
</div>
</li>
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<svg class="w-full h-full" viewbox="0 0 36 36">
<path class="stroke-primary/20 dark:stroke-primary/40" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke-width="3"></path>
<path class="stroke-primary" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831" fill="none" stroke-dasharray="75, 100" stroke-linecap="round" stroke-width="3"></path>
</svg>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white">Project X - Phase 1</p>
<div class="flex items-center gap-2 mt-0.5">
<p class="text-sm text-slate-500 dark:text-slate-400">10:00 AM</p>
<div class="flex -space-x-2">
<img alt="" class="inline-block h-5 w-5 rounded-full ring-2 ring-white dark:ring-slate-800/50" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<img alt="" class="inline-block h-5 w-5 rounded-full ring-2 ring-white dark:ring-slate-800/50" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
</div>
</div>
</div>
</div>
<button class="w-6 h-6 rounded-full border-2 border-slate-300 dark:border-slate-600 flex items-center justify-center"></button>
</li>
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<div class="absolute inset-0 rounded-full border-2 border-orange-500/30 dark:border-orange-400/40"></div>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white">Client Meeting</p>
<p class="text-sm text-slate-500 dark:text-slate-400">11:00 AM</p>
</div>
</div>
<button class="w-6 h-6 rounded-full border-2 border-slate-300 dark:border-slate-600 flex items-center justify-center"></button>
</li>
</ul>
<div class="bg-white dark:bg-slate-800/50 p-4 rounded-xl mt-6 mb-4">
<h3 class="text-base font-bold text-slate-900 dark:text-white mb-3">Leaderboards</h3>
<div class="space-y-3">
<div class="flex items-center gap-3">
<span class="text-lg font-bold text-slate-400 dark:text-slate-500">1</span>
<img alt="" class="h-10 w-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<div class="flex-1">
<p class="font-semibold text-slate-800 dark:text-white">You</p>
<p class="text-sm text-slate-500 dark:text-slate-400">Streak: 24 days</p>
</div>
<div class="text-right">
<p class="font-semibold text-primary">1250 pts</p>
<span class="text-xs text-green-500">+50 today</span>
</div>
</div>
<div class="flex items-center gap-3">
<span class="text-lg font-bold text-slate-400 dark:text-slate-500">2</span>
<img alt="" class="h-10 w-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<div class="flex-1">
<p class="font-semibold text-slate-800 dark:text-white">Jane Doe</p>
<p class="text-sm text-slate-500 dark:text-slate-400">Streak: 21 days</p>
</div>
<p class="font-semibold text-slate-600 dark:text-slate-300">1180 pts</p>
</div>
<div class="flex items-center gap-3">
<span class="text-lg font-bold text-slate-400 dark:text-slate-500">3</span>
<img alt="" class="h-10 w-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<div class="flex-1">
<p class="font-semibold text-slate-800 dark:text-white">John Smith</p>
<p class="text-sm text-slate-500 dark:text-slate-400">Streak: 18 days</p>
</div>
<p class="font-semibold text-slate-600 dark:text-slate-300">1050 pts</p>
</div>
</div>
</div>
</div>
</main>
<footer class="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
<div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="dashboard.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.10Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="focus.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M221.87,83.16A104.1,104.1,0,1,1,195.67,49l22.67-22.68a8,8,0,0,1,11.32,11.32L167.6,99.71h0l-37.71,37.71-23.95,23.95a40,40,0,0,0,62-35.67,8,8,0,1,1,16-.9,56,56,0,0,1-95.5,42.79h0a56,56,0,0,1,73.13-84.43L184.3,60.39a87.88,87.88,0,1,0,23.13,29.67,8,8,0,0,1,14.44-6.9Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Focus</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="track.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Track</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="achieve.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M232,64H208V56a16,16,0,0,0-16-16H64A16,16,0,0,0,48,56v8H24A16,16,0,0,0,8,80V96a40,40,0,0,0,40,40h3.65A80.13,80.13,0,0,0,120,191.61V216H96a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16H136V191.58c31.94-3.23,58.44-25.64,68.08-55.58H208a40,40,0,0,0,40-40V80A16,16,0,0,0,232,64ZM48,120A24,24,0,0,1,24,96V80H48v32q0,4,.39,8Zm144-8.9c0,35.52-28.49,64.64-63.51,64.9H128a64,64,0,0,1-64-64V56H192ZM232,96a24,24,0,0,1-24,24h-.5a81.81,81.81,0,0,0,.5-8.9V80h24Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Achieve</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="tasks.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z"></path>
</svg>
</div>
<p class="text-xs font-semibold">Tasks</p>
</a>
</div>
</footer>
</div>
</body></html>