<!DOCTYPE html>
<html class="dark" lang="en">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>IsotopeAI</title>
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet" />

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#F4C753",
                        "background-light": "#f8f7f6",
                        "background-dark": "#221d10",
                        "card-dark": "#1C1C1E"
                    },
                    fontFamily: {
                        display: "Inter"
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                        lg: "1rem",
                        xl: "1.5rem",
                        full: "9999px"
                    }
                }
            }
        };
    </script>
    <style>


        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display text-gray-800 dark:text-gray-200">
    <div class="flex h-screen flex-col">
        <header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="w-10"></div>
                <div class="flex flex-col items-center">
                    <h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">IsotopeAI</h1>
                    <p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Dashboard</p>
                </div>
                <button class="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="20" height="20">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                    </svg>
                </button>
            </div>
        </header>
        <main class="flex-1 overflow-y-auto px-6 py-8 space-y-8">
            <div class="space-y-2">
                <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Good morning, Friend!</h1>
                <p class="text-gray-600 dark:text-gray-400">Ready to achieve your goals today?</p>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Quick Start</h2>
                    <button class="px-6 py-3 rounded-full bg-gradient-to-r from-primary to-yellow-400 text-gray-900 font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                        Focus
                    </button>
                </div>
                <div class="grid grid-cols-2 gap-6">
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">2h 15m</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Today's Focus</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">🔥 12</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Current Streak</p>
                    </div>
                </div>
            </div>
            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">My Progress</h2>
                    <div class="flex items-center space-x-2 bg-gray-100/80 dark:bg-gray-700/50 p-1 rounded-full text-sm font-medium">
                        <button class="px-4 py-2 rounded-full bg-white dark:bg-gray-600 text-gray-800 dark:text-gray-100 shadow-sm">Weekly</button>
                        <button class="px-4 py-2 rounded-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">Monthly</button>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-8">
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">15.5h</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Weekly Study</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">28 days</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Longest Streak</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">85%</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">Goal Comp.</p>
                    </div>
                </div>

                <div class="h-48 w-full bg-gray-50/50 dark:bg-gray-700/20 rounded-xl p-4">
                    <div class="flex h-full items-end justify-between space-x-2">
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[60%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Mon</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[80%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Tue</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary rounded-t-lg h-[95%] hover:bg-primary/80 transition-colors shadow-lg"></div>
                            <span class="text-xs text-primary font-bold">Wed</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[50%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Thu</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[70%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Fri</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-primary/30 rounded-t-lg h-[40%] hover:bg-primary/50 transition-colors"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Sat</span>
                        </div>
                        <div class="flex-1 flex flex-col items-center space-y-2">
                            <div class="w-full bg-gray-300 dark:bg-gray-600 rounded-t-lg h-[20%]"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">Sun</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid gap-6 sm:grid-cols-2">
                <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                    <div class="flex items-center justify-between mb-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Focus Time</p>
                        <div class="flex items-center gap-1 text-sm">
                            <p class="font-medium text-green-500">+15%</p>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mb-2">12h 30m</p>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">This Week</p>
                    <div class="h-32 bg-gray-50/50 dark:bg-gray-700/20 rounded-xl p-3">
                        <div class="grid h-full grid-cols-12 items-end gap-1">
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 60%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 15%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 25%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 65%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 20%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 70%"></div>
                            <div class="rounded-t-lg bg-primary hover:bg-primary/80 transition-colors shadow-lg" style="height: 85%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 45%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 30%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 80%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 95%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 10%"></div>
                        </div>
                    </div>
                </div>
                <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                    <div class="flex items-center justify-between mb-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tasks Completed</p>
                        <div class="flex items-center gap-1 text-sm">
                            <p class="font-medium text-green-500">+20%</p>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mb-2">25</p>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">This Week</p>
                    <div class="h-32 bg-gray-50/50 dark:bg-gray-700/20 rounded-xl p-3">
                        <div class="grid h-full grid-cols-7 items-end gap-2">
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 40%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 60%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 30%"></div>
                            <div class="rounded-t-lg bg-primary hover:bg-primary/80 transition-colors shadow-lg" style="height: 80%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 40%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 30%"></div>
                            <div class="rounded-t-lg bg-primary/50 hover:bg-primary/70 transition-colors" style="height: 10%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <h2 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Next Up</h2>
                <div class="flex items-center space-x-4 p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                    <div class="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-xl">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-blue-500">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="font-semibold text-gray-900 dark:text-white">Chemistry Lab Report</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Due in 2 days</p>
                    </div>
                    <button class="p-3 rounded-full bg-primary/20 text-primary hover:bg-primary/30 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <h2 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Achieve Your Goals</h2>
                <div class="space-y-6">
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-800 dark:text-gray-200">Master Calculus</span>
                            <span class="font-medium text-gray-500 dark:text-gray-400">75%</span>
                        </div>
                        <div class="w-full bg-gray-200/80 dark:bg-gray-700/50 rounded-full h-3">
                            <div class="bg-gradient-to-r from-cyan-400 to-blue-500 h-3 rounded-full shadow-sm" style="width: 75%"></div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-800 dark:text-gray-200">Ace Physics Exam</span>
                            <span class="font-medium text-gray-500 dark:text-gray-400">50%</span>
                        </div>
                        <div class="w-full bg-gray-200/80 dark:bg-gray-700/50 rounded-full h-3">
                            <div class="bg-gradient-to-r from-green-400 to-teal-500 h-3 rounded-full shadow-sm" style="width: 50%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                <h2 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Today's Tasks & Habits</h2>
                <div class="space-y-4 mb-6">
                    <div class="flex items-center space-x-4 p-3 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <button class="w-6 h-6 border-2 border-red-400 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"></button>
                        <p class="font-medium flex-1 text-gray-900 dark:text-white">Review Quantum Mechanics notes</p>
                        <span class="text-xs font-medium text-red-400 bg-red-400/20 px-3 py-1 rounded-full">Urgent</span>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
                        <button class="w-6 h-6 border-2 border-gray-400 rounded-full hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"></button>
                        <p class="font-medium flex-1 text-gray-900 dark:text-white">Practice 10 integrals</p>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl opacity-75">
                        <button class="w-6 h-6 border-2 border-primary rounded-full flex items-center justify-center bg-primary/10">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-primary">
                                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5"/>
                            </svg>
                        </button>
                        <p class="font-medium flex-1 line-through text-gray-500 dark:text-gray-400">Daily meditation</p>
                    </div>
                </div>
                <div class="relative">
                    <input class="w-full bg-gray-50/80 dark:bg-gray-700/30 rounded-xl py-4 pl-4 pr-14 focus:ring-2 focus:ring-primary/50 border-transparent focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400" placeholder="Add a quick task..." type="text" />
                    <button class="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-gradient-to-r from-primary to-yellow-400 text-gray-900 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"/>
                        </svg>
                    </button>
                </div>
            </div>
        </main>
        <footer class="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
            <div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="dashboard.html">
                    <div class="p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
                        </svg>
                    </div>
                    <p class="text-xs font-semibold">Home</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="focus.html">
                    <div class="p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Focus</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="track.html">
                    <div class="p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"/>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Track</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="achieve.html">
                    <div class="p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"/>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Achieve</p>
                </a>
                <a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="tasks.html">
                    <div class="p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"/>
                        </svg>
                    </div>
                    <p class="text-xs font-medium">Tasks</p>
                </a>
            </div>
        </footer>
    </div>
</body>
</html>