<!DOCTYPE html>

<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>IsotopeAI: Study Session Analysis</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>tailwind.config = {darkMode: "class", theme: {extend: {colors: {primary: "#F4C753", "background-light": "#f8f7f6", "background-dark": "#221d10"}, fontFamily: {display: "Inter"}, borderRadius: {DEFAULT: "0.5rem", lg: "1rem", xl: "1.5rem", full: "9999px"}}}};</script>
<style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .chart-bar {
            background: #1173d4;
        }
        .chart-line {
            stroke: #1173d4;
        }
        .chart-area {
            fill: url(#chartGradient);
        }
    </style>
<style>
    /* No min-height for body in track.html */
  </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display">
<div class="flex h-screen flex-col">
<div>
<header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
<div class="flex items-center justify-between px-6 py-4">
<div class="w-10"></div>
<div class="flex flex-col items-center">
<h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Track</h1>
<p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Analytics</p>
</div>
<button class="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 transition-colors">
<svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20" xmlns="http://www.w3.org/2000/svg">
<path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z"></path>
</svg>
</button>
</div>
</header>
<main class="flex-1 overflow-y-auto px-6 py-8 space-y-8">
<section class="mb-8">
<h2 class="text-2xl font-bold text-black dark:text-white mb-4">Session Overview</h2>
<div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
<div class="flex flex-col gap-2 rounded-lg bg-primary/10 p-4 text-center">
<p class="text-sm font-medium text-black/70 dark:text-white/70">Total Study Time</p>
<p class="text-2xl font-bold text-black dark:text-white">3h 45m</p>
</div>
<div class="flex flex-col gap-2 rounded-lg bg-primary/10 p-4 text-center">
<p class="text-sm font-medium text-black/70 dark:text-white/70">Focus Score</p>
<p class="text-2xl font-bold text-black dark:text-white">88%</p>
</div>
<div class="flex flex-col gap-2 rounded-lg bg-primary/10 p-4 text-center col-span-2 sm:col-span-1">
<p class="text-sm font-medium text-black/70 dark:text-white/70">Tasks Completed</p>
<p class="text-2xl font-bold text-black dark:text-white">12</p>
</div>
</div>
</section>
<section class="mb-8 rounded-xl bg-background-light p-4 shadow-sm dark:bg-primary/10">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-lg font-bold text-black dark:text-white">Focus Score Trend</h3>
<p class="text-sm text-black/60 dark:text-white/60">Last 7 Days</p>
</div>
<div class="text-right">
<p class="text-3xl font-bold text-black dark:text-white">88%</p>
<p class="text-sm font-medium text-green-500">+5%</p>
</div>
</div>
<div class="h-48">
<svg fill="none" height="100%" preserveaspectratio="none" viewbox="0 0 472 150" width="100%" xmlns="http://www.w3.org/2000/svg">
<defs>
<lineargradient gradientunits="userSpaceOnUse" id="chartGradient" x1="0" x2="0" y1="0" y2="150">
<stop stop-color="#1173d4" stop-opacity="0.4"></stop>
<stop offset="1" stop-color="#1173d4" stop-opacity="0"></stop>
</lineargradient>
</defs>
<path class="chart-area" d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25V150H0V109Z"></path>
<path class="chart-line" d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25" stroke-linecap="round" stroke-width="3"></path>
</svg>
</div>
<div class="flex justify-around text-xs font-bold text-black/60 dark:text-white/60 mt-2">
<p>Mon</p><p>Tue</p><p>Wed</p><p>Thu</p><p>Fri</p><p>Sat</p><p>Sun</p>
</div>
</section>
<section class="mb-8 rounded-xl bg-background-light p-4 shadow-sm dark:bg-primary/10">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-lg font-bold text-black dark:text-white">Task Completion</h3>
<p class="text-sm text-black/60 dark:text-white/60">This Week</p>
</div>
<div class="text-right">
<p class="text-3xl font-bold text-black dark:text-white">12</p>
<p class="text-sm font-medium text-green-500">+10%</p>
</div>
</div>
<div class="grid grid-cols-7 gap-3 items-end h-32 pt-4">
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 70%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 20%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 30%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 40%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 80%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 50%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 100%;"></div></div>
</div>
<div class="flex justify-around text-xs font-bold text-black/60 dark:text-white/60 mt-2">
<p>Mon</p><p>Tue</p><p>Wed</p><p>Thu</p><p>Fri</p><p>Sat</p><p>Sun</p>
</div>
</section>
<section>
<h2 class="text-2xl font-bold text-black dark:text-white mb-4">Task Manager</h2>
<div class="space-y-2">
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black dark:text-white">Complete Calculus Assignment</p>
<p class="text-sm text-black/60 dark:text-white/60">Math</p>
</div>
</div>
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black dark:text-white">Review Physics Notes</p>
<p class="text-sm text-black/60 dark:text-white/60">Science</p>
</div>
</div>
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input checked="" class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black/50 dark:text-white/50 line-through">Read Chapter 5</p>
<p class="text-sm text-black/40 dark:text-white/40 line-through">History</p>
</div>
</div>
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black dark:text-white">Write Essay Outline</p>
<p class="text-sm text-black/60 dark:text-white/60">English</p>
</div>
</div>
</div>
</section>
</main>
</div>
<footer class="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
<div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="dashboard.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.10Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="focus.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M221.87,83.16A104.1,104.1,0,1,1,195.67,49l22.67-22.68a8,8,0,0,1,11.32,11.32L167.6,99.71h0l-37.71,37.71-23.95,23.95a40,40,0,0,0,62-35.67,8,8,0,1,1,16-.9,56,56,0,0,1-95.5,42.79h0a56,56,0,0,1,73.13-84.43L184.3,60.39a87.88,87.88,0,1,0,23.13,29.67,8,8,0,0,1,14.44-6.9Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Focus</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="track.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z"></path>
</svg>
</div>
<p class="text-xs font-semibold">Track</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="achieve.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M232,64H208V56a16,16,0,0,0-16-16H64A16,16,0,0,0,48,56v8H24A16,16,0,0,0,8,80V96a40,40,0,0,0,40,40h3.65A80.13,80.13,0,0,0,120,191.61V216H96a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16H136V191.58c31.94-3.23,58.44-25.64,68.08-55.58H208a40,40,0,0,0,40-40V80A16,16,0,0,0,232,64ZM48,120A24,24,0,0,1,24,96V80H48v32q0,4,.39,8Zm144-8.9c0,35.52-28.49,64.64-63.51,64.9H128a64,64,0,0,1-64-64V56H192ZM232,96a24,24,0,0,1-24,24h-.5a81.81,81.81,0,0,0,.5-8.9V80h24Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Achieve</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="tasks.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Tasks</p>
</a>
</div>
</footer>
</div>
</body></html>