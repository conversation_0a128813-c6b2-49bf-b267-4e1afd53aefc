<!DOCTYPE html>

<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>IsotopeAI: Focus, Track, Achieve</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<script>tailwind.config = {darkMode: "class", theme: {extend: {colors: {primary: "#F4C753", "background-light": "#f8f7f6", "background-dark": "#221d10"}, fontFamily: {display: "Inter"}, borderRadius: {DEFAULT: "0.5rem", lg: "1rem", xl: "1.5rem", full: "9999px"}}}};</script>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display">
<div class="flex flex-col min-h-screen">
<header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
<div class="flex items-center justify-between px-6 py-4">
<div class="w-10"></div>
<div class="flex flex-col items-center">
<h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Achieve</h1>
<p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Goals & Progress</p>
</div>
<button class="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 transition-colors">
<span class="material-symbols-outlined text-lg">settings</span>
</button>
</div>
</header>
<main class="flex-grow p-4 space-y-6">
<section class="bg-white dark:bg-gray-800/20 rounded-xl shadow-sm overflow-hidden">
<div class="h-40 bg-cover bg-center" style="
              background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuB3SlITj8k4luxJacmGoYHkjnn33NetDBk5NkUmkdvX0GPpFZkuLQhe8KEXfNGEbQA-lG24ZNYGG5bF7FdDMGoMDQw_HxqRLsrZMcanqkcUUqKq3N5sX_ent4Z7cbs9MPdO2c0ZR3CNbLir--dWxd9_vw4Ir7GL99pOd_kaz6OiPVjtMWt9S6_DQNv3u2K9iVogjQn2dVeRDtIav1Sn1txko1DTZFscaMqSk0ajnA8pjLZAGNko_t4yM4zh7Gsv-HWCYvEhZnGsS5E');
            "></div>
<div class="p-4">
<h2 class="text-lg font-bold text-gray-900 dark:text-white">
              Complete your goal
            </h2>
<p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Complete all your tasks to achieve your goal.
            </p>
<div class="flex items-center gap-4 mt-3">
<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
<div class="bg-primary h-2 rounded-full" style="width: 100%"></div>
</div>
<span class="text-sm font-semibold text-gray-900 dark:text-white">100%</span>
</div>
</div>
</section>
<section>
<div class="flex items-center justify-between mb-4">
<h2 class="text-xl font-bold text-gray-900 dark:text-white">
              Subjects
            </h2>
<select class="form-select w-40 rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800/20 text-gray-900 dark:text-white focus:border-primary focus:ring-primary">
<option>All Subjects</option>
<option>Mathematics</option>
<option>Physics</option>
<option>Chemistry</option>
</select>
</div>
<div class="space-y-4">
<div class="flex items-center gap-4 p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-16 h-16 rounded-lg bg-cover bg-center flex-shrink-0" style="
                  background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuC3mTFBz_vrVT137JPuhlfsj-k2YolMlsq4a3b6KMdhLEGwClrH-W_db4Q3O6aiQ3qzRejErGRvas53h3DkKv_Ql8l5N-trTl7mwdHLfc1w0n2T9sge0HArBMdw3VDv4RKDdW6fzbQ74TTnYh-gfqQbNziZOMzLhCZ_KnGOQBXppvOLjA6_BEGOzWND1WbutP07WlrU93Ou-CuYAJSwAUOHaYBbGwqlvgljHwzP5slBjIHYJVa7YqQTnrAeFPeFpK3iVWVStZF3OC8');
                "></div>
<div class="flex-grow">
<p class="font-bold text-gray-900 dark:text-white">
                  Mathematics
                </p>
<p class="text-sm text-gray-500 dark:text-gray-400">
                  100% completed
                </p>
</div>
<span class="material-symbols-outlined text-gray-400 dark:text-gray-500">
                arrow_forward_ios
              </span>
</div>
<div class="flex items-center gap-4 p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-16 h-16 rounded-lg bg-cover bg-center flex-shrink-0" style="
                  background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBnyIpSaSLMeVOptTipylwYOAo_QaPQE60pl-ZFkZHRBFaroFhlV_-FVno9-FDsiGBtDPVxjyENZe5rDEoakYpxQdGjrybpUFkYdVmtr1-kfwUnso-QPkj5-QZOhhpFiBdU9Ka-LsgmPsnsnWc8miK-Tna_HpEJl1wDL3VMGyqDnbh2RVAaG3w3-kzXzE8GzhAd-03urg343jpl2o0JUFd1Pivd-7FnUTzveRGbgKdt1WbPUB4bbXQryTJFPpcnq09hxiUnE734Bjk');
                "></div>
<div class="flex-grow">
<p class="font-bold text-gray-900 dark:text-white">Physics</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
                  100% completed
                </p>
</div>
<span class="material-symbols-outlined text-gray-400 dark:text-gray-500">
                arrow_forward_ios
              </span>
</div>
<div class="flex items-center gap-4 p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-16 h-16 rounded-lg bg-cover bg-center flex-shrink-0" style="
                  background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuCRmZ4n6wwu-5KknThFNloVOgYNmJ4f6jH2S9Pfp3kJuf0uA33dQOBGUoxlO6w23CDnALGo6gNfqo74oNYCYZ4oGHzhs5Xmert1G54dLfYQ9yQ2MqwmSsIvVBfG0lYWMGd9iIeMBNNAeGxf9hnz8agYGBZSHqQ4rqfcjmmJvr2jxV3BPvY9PWv1FbOR1ABrY9ByjdweZiwaoNJcpeVS5Hv7jrU8x_lpmiRDH1Sy7lPcn71yoDUZBsDi2fP8L8T44cLo6DVtppiKF3w');
                "></div>
<div class="flex-grow">
<p class="font-bold text-gray-900 dark:text-white">Chemistry</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
                  100% completed
                </p>
</div>
<span class="material-symbols-outlined text-gray-400 dark:text-gray-500">
                arrow_forward_ios
              </span>
</div>
</div>
</section>
<section>
<h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
            Exams &amp; Mock Tests
          </h2>
<div class="grid grid-cols-2 gap-4">
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuDeqdOuI8Ro16cZZc-A9peijDAOSO-X-D85uFk8sWDJRPrDIvPVoPGSCWm2L7Vn-dpq-d2ivoGfmSL9aXlZgY6Y7nXY_7lZrRpt5kAxAjJdJ2NiOf8S0iLdxJH-zyB0U32H3v6EdJ2Vgy66Mj2sV3tfFsWgAbwldIGEzUyQzKlU7y2JrYOt1Da_5Js7zIbDfwwcgl13qKYXVADE0IawX5Vi95F_ZpwfUrjebTRCO4271SayMtyYuUJ3svyy-w7wEqEOHn1H0a5FgKY')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">JEE Main</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBBWfTZ1Tq0OBnuyvYIsfWntcN_dDAv8kcyzO4RL4GLDA7xHGLWbOswtRdLoXa4xTc5DcKPmw0w_1sp3KpLvoySM4xdvH0WvQG0y6mbQmNqDweRNijFTnVanv8O3ZqZXkFWfUT4MhjhLyupMSMBoTHmupmLdafn5UDS_fhE71BZJT4EsgsSJR6Q58YYO2y5GZ92rSh7u8II_ZC7s_rInW17XsRqAtRst1_moriqDmua01UnIT17J69pLffB8hqC24u7yik2Ka09YAs')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">JEE Advanced</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
</div>
</section>
<section>
<h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
            Study Plans
          </h2>
<div class="grid grid-cols-2 gap-4">
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBXViybLg7q6BOq_zCpu-KloaZccQqbl8_ED47GyEgYAbRQ19gWOUJWf79Gc3liej433seYOMEC6jluQoDbIPnuBnrpzgwXrBJku6eYQp-xqoQg359LflSRvZGEm95SO4IK0YmBrQfXf4ysUOeUnwV0bbXyFDFBydQOzG46-4GbexiT99-0wG1vnzdtveouFOQ2qpIV8gwXGYITBtk3R3NMnw2eixXDM2GKC-IOnZy-nK0e90zPMRtR6HvWk8GOUMEKm88gFa7aQPw')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">Daily Plan</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuChSd1YdHQVFCLhn5ZlvAO6Fafi26bbvRBEp6EaAZ2DZjhkiTw3Nq6DA0P-SV7R-ab9dEHk1rb2bC3e3Xqjo0gMlVGG67CboNMTk6XTjB6ekFc9Xz2zyW5yK157eu2AsX4j1noftmRawXfIOceNa7FbOyXyJD6-fOqHT59FJ50uRy1Y_tbyjAa1nhCElV2E_EgTYePlw9ur4XfVtzt1--sOrGDXqfFezFx3u6V8C7p8ZWrJ7nmGHq5cDWQG_H2SOApGAyaLLve5-vM')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">Weekly Plan</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
</div>
</section>
</main>
<footer class="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
<div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="dashboard.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.10Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="focus.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M221.87,83.16A104.1,104.1,0,1,1,195.67,49l22.67-22.68a8,8,0,0,1,11.32,11.32L167.6,99.71h0l-37.71,37.71-23.95,23.95a40,40,0,0,0,62-35.67,8,8,0,1,1,16-.9,56,56,0,0,1-95.5,42.79h0a56,56,0,0,1,73.13-84.43L184.3,60.39a87.88,87.88,0,1,0,23.13,29.67,8,8,0,0,1,14.44-6.9Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Focus</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="track.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Track</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="achieve.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M232,64H208V56a16,16,0,0,0-16-16H64A16,16,0,0,0,48,56v8H24A16,16,0,0,0,8,80V96a40,40,0,0,0,40,40h3.65A80.13,80.13,0,0,0,120,191.61V216H96a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16H136V191.58c31.94-3.23,58.44-25.64,68.08-55.58H208a40,40,0,0,0,40-40V80A16,16,0,0,0,232,64ZM48,120A24,24,0,0,1,24,96V80H48v32q0,4,.39,8Zm144-8.9c0,35.52-28.49,64.64-63.51,64.9H128a64,64,0,0,1-64-64V56H192ZM232,96a24,24,0,0,1-24,24h-.5a81.81,81.81,0,0,0,.5-8.9V80h24Z"></path>
</svg>
</div>
<p class="text-xs font-semibold">Achieve</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="tasks.html">
<div class="p-1">
<svg fill="currentColor" height="22" viewBox="0 0 256 256" width="22" xmlns="http://www.w3.org/2000/svg">
<path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z"></path>
</svg>
</div>
<p class="text-xs font-medium">Tasks</p>
</a>
</div>
</footer>
</div>
</body></html>